{"use_unipile": true, "unipile": {"api_url": "https://api1.unipile.com:13115", "api_key": "", "account_id": "", "enabled": true}, "client_key": "", "client_secret": "", "access_token": "", "refresh_token": "", "open_id": "", "api_version": "v1.3", "base_url": "https://open-api.tiktok.com", "rate_limit": {"requests_per_second": 10, "requests_per_minute": 100, "requests_per_hour": 1000}, "settings": {"auto_retry": true, "max_retries": 3, "retry_delay": 2, "log_level": "INFO"}, "message_templates": {"welcome": "🎵 Welcome to our TikTok! Thanks for following us.", "follow_up": "✨ Thanks for engaging with our content! Check out our latest videos.", "promotional": "🔥 Don't miss our latest TikTok content and special offers!"}, "video_settings": {"max_duration": 60, "supported_formats": ["mp4", "mov", "avi"], "max_file_size_mb": 500, "privacy_level": "PUBLIC_TO_EVERYONE"}, "comment_settings": {"auto_reply": false, "filter_keywords": ["spam", "inappropriate"], "max_reply_length": 150, "auto_mention": true, "sentiment_analysis": true, "priority_threshold": 2, "engagement_tracking": true}}