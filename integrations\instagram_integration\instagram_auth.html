<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Business API Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        h1 {
            background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #e6683c;
            background-color: #f9f9f9;
            border-radius: 0 10px 10px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            border-color: #e6683c;
            outline: none;
        }
        button {
            background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #e6683c;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📷 Instagram Business API Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> Instagram messaging requires a Business or Creator account.
            This integration uses Unipile API for unified Instagram messaging.
        </div>

        <div class="step">
            <h3>🚀 Unipile API Setup</h3>
            <div class="info">
                <strong>Unipile provides unified access to Instagram messaging with simplified authentication.</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="connectUnipile()">Connect via Unipile</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>
        </div>

        <div class="step">
            <h3>Step 1: Account Requirements</h3>
            <p>1. Ensure you have an Instagram Business or Creator account</p>
            <p>2. Sign up for Unipile API at <a href="https://unipile.com" target="_blank">unipile.com</a></p>
            <p>3. Get your Unipile API key from the dashboard</p>
            <p>4. Connect your Instagram account via Unipile dashboard</p>
        </div>

        <div class="step">
            <h3>Step 2: Account Information</h3>
            <p>Once configured, you can get information about your Instagram account:</p>
            <button type="button" onclick="getAccountInfo()">Get Account Info</button>
            <div id="accountInfoResult"></div>
        </div>

        <div class="step">
            <h3>Step 7: Test Direct Messaging</h3>
            <div class="info">
                <strong>Important:</strong> You can only send messages to users who have initiated contact with your business account first.
            </div>
            
            <div class="form-group">
                <label for="testUserId">User Instagram ID:</label>
                <input type="text" id="testUserId" placeholder="Instagram user ID (IGSID)">
            </div>
            <div class="form-group">
                <label for="testMessage">Test Message:</label>
                <textarea id="testMessage" rows="3" placeholder="Hello! Thanks for reaching out to us on Instagram."></textarea>
            </div>
            <button type="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="testResult"></div>
        </div>

        <div class="step">
            <h3>Step 8: Bulk Messaging</h3>
            <div class="info">
                <strong>Send messages to multiple recipients using unified API (Unipile + Facebook Graph API fallback)</strong>
            </div>

            <div class="form-group">
                <label for="bulkRecipients">Recipient IDs (one per line):</label>
                <textarea id="bulkRecipients" rows="4" placeholder="user_id_1&#10;user_id_2&#10;user_id_3"></textarea>
            </div>
            <div class="form-group">
                <label for="bulkMessage">Bulk Message:</label>
                <textarea id="bulkMessage" rows="3" placeholder="Hello! This is a bulk message from our Instagram account."></textarea>
            </div>
            <div class="form-group">
                <label for="messageDelay">Delay between messages (seconds):</label>
                <input type="number" id="messageDelay" value="3" min="1" max="60">
            </div>
            <button type="button" onclick="sendBulkMessages()">Send Bulk Messages</button>
            <div id="bulkResult"></div>
        </div>

        <div class="step">
            <h3>Step 9: Content Publishing</h3>
            <p>You can also publish content to Instagram:</p>
            <div class="form-group">
                <label for="imageUrl">Image URL:</label>
                <input type="text" id="imageUrl" placeholder="https://example.com/image.jpg">
            </div>
            <div class="form-group">
                <label for="caption">Caption:</label>
                <textarea id="caption" rows="3" placeholder="Your Instagram post caption with #hashtags"></textarea>
            </div>
            <button type="button" onclick="createPost()">Create Post</button>
            <button type="button" onclick="createStory()">Create Story</button>
            <div id="contentResult"></div>
        </div>

        <div class="step">
            <h3>Step 10: Comment Replies & Story Interactions</h3>
            <div class="info">
                <strong>💡 Recommended: Use comment replies and story interactions for better engagement without DM restrictions</strong>
            </div>

            <h4>Comment Replies</h4>
            <div class="form-group">
                <label for="commentId">Comment ID:</label>
                <input type="text" id="commentId" placeholder="Comment ID to reply to">
            </div>
            <div class="form-group">
                <label for="commentReply">Reply Text:</label>
                <textarea id="commentReply" rows="2" placeholder="Thanks for your comment! 😊"></textarea>
            </div>
            <button type="button" onclick="replyToComment()">Reply to Comment</button>
            <div id="commentReplyResult"></div>

            <h4>Get Post Comments</h4>
            <div class="form-group">
                <label for="postId">Post/Media ID:</label>
                <input type="text" id="postId" placeholder="Instagram post/media ID">
            </div>
            <button type="button" onclick="getPostComments()">Get Comments</button>
            <div id="commentsResult"></div>
        </div>

        <div class="step">
            <h3>Step 11: Getting User IDs</h3>
            <p>To send messages, you need Instagram-scoped user IDs (IGSID):</p>
            <ul>
                <li><strong>Webhook:</strong> Set up webhook to receive message events</li>
                <li><strong>Conversations API:</strong> Get existing conversations</li>
                <li><strong>User interaction:</strong> Users must message your business first</li>
            </ul>
            <button type="button" onclick="getConversations()">Get Conversations</button>
            <div id="conversationsResult"></div>
        </div>

        <div class="step">
            <h3>Step 12: Instagram Messaging Limitations</h3>
            <div class="warning">
                <strong>⚠️ Instagram Messaging Restrictions:</strong>
                <ul>
                    <li><strong>Direct Messages:</strong> Limited to users who message your business first</li>
                    <li><strong>24-hour rule:</strong> Can only message users who contacted you within 24 hours</li>
                    <li><strong>Business verification:</strong> Some features require Meta business verification</li>
                </ul>
            </div>

            <div class="info">
                <strong>💡 Recommended Alternatives:</strong>
                <ul>
                    <li><strong>Comment Replies:</strong> Respond to comments on your posts</li>
                    <li><strong>Story Interactions:</strong> Engage with story mentions and reactions</li>
                    <li><strong>Content Publishing:</strong> Create posts and stories to engage audience</li>
                    <li><strong>Hashtag Monitoring:</strong> Track mentions and branded hashtags</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <h3>Step 13: Rate Limits & Best Practices</h3>
            <ul>
                <li><strong>Rate Limits:</strong> 200 messages per minute, 1000 per hour</li>
                <li><strong>24-hour rule:</strong> Can only message users who contacted you within 24 hours</li>
                <li><strong>Content policy:</strong> Follow Instagram's community guidelines</li>
                <li><strong>Business use only:</strong> Don't spam or send unsolicited messages</li>
                <li><strong>Focus on engagement:</strong> Use comments and stories for broader reach</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkUnipileStatus, 30000);

        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            fetch('/api/instagram/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connected successfully!</div>';
                    checkUnipileStatus(); // Refresh status
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Connection error: ' + error.message + '</div>';
            });
        }

        function checkUnipileStatus() {
            fetch('/api/instagram/connection-status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const status = data.status;
                    let html = '<div class="info"><strong>📊 Connection Status:</strong><br>';

                    // Unipile status
                    if (status.unipile.available) {
                        if (status.unipile.connected) {
                            html += '🟢 Unipile: Connected (' + status.unipile.accounts.length + ' accounts)<br>';
                            status.unipile.accounts.forEach(account => {
                                html += '&nbsp;&nbsp;• ' + (account.username || account.name || account.account_id) + '<br>';
                            });
                        } else {
                            html += '🟡 Unipile: Available but not connected<br>';
                        }
                    } else {
                        html += '🔴 Unipile: Not available<br>';
                    }

                    html += '</div>';
                    statusDiv.innerHTML = html;
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Status check error: ' + error.message + '</div>';
            });
        }

        function saveConfig() {
            const config = {
                unipile_api_key: document.getElementById('unipileApiKey').value
            };
            
            fetch('/api/instagram/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getAccountInfo() {
            fetch('/api/instagram/account-info')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('accountInfoResult');
                if (data.success) {
                    const info = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>Account Information:</strong><br>
                            Username: @${info.username}<br>
                            Name: ${info.name}<br>
                            Followers: ${info.followers_count}<br>
                            Following: ${info.follows_count}<br>
                            Posts: ${info.media_count}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get account info: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('accountInfoResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendTestMessage() {
            const userId = document.getElementById('testUserId').value;
            const message = document.getElementById('testMessage').value;

            if (!userId || !message) {
                document.getElementById('testResult').innerHTML =
                    '<div class="error">❌ Please enter both user ID and message</div>';
                return;
            }

            // Show sending status
            document.getElementById('testResult').innerHTML =
                '<div class="info">📤 Sending message via unified API...</div>';

            fetch('/api/instagram/send-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: userId,
                    message: message,
                    use_unipile: true
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    const method = data.method || 'unknown';
                    resultDiv.innerHTML = `<div class="success">✅ Test message sent successfully via ${method}!</div>`;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send test message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendBulkMessages() {
            const recipients = document.getElementById('bulkRecipients').value.split('\n').filter(id => id.trim());
            const message = document.getElementById('bulkMessage').value;
            const delay = parseFloat(document.getElementById('messageDelay').value) || 3;

            if (recipients.length === 0 || !message) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter recipient IDs and message</div>';
                return;
            }

            // Show sending status
            document.getElementById('bulkResult').innerHTML =
                `<div class="info">📤 Sending bulk messages to ${recipients.length} recipients...</div>`;

            fetch('/api/instagram/send-bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: message,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkResult');
                if (data.success) {
                    const results = data.results || [];
                    const successful = results.filter(r => !r.result.error).length;
                    const failed = results.length - successful;

                    let html = `<div class="success">✅ Bulk messaging completed!<br>`;
                    html += `Successful: ${successful}, Failed: ${failed}<br><br>`;
                    html += '<strong>Details:</strong><br>';

                    results.forEach((result, index) => {
                        const status = result.result.error ? '❌' : '✅';
                        const method = result.method || 'unknown';
                        html += `${status} ${result.recipient_id} (${method})<br>`;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Bulk messaging failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function replyToComment() {
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('commentReply').value;

            if (!commentId || !replyText) {
                document.getElementById('commentReplyResult').innerHTML =
                    '<div class="error">❌ Please enter comment ID and reply text</div>';
                return;
            }

            fetch('/api/instagram/reply-comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    comment_id: commentId,
                    reply_text: replyText
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('commentReplyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Comment reply sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to reply: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('commentReplyResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getPostComments() {
            const postId = document.getElementById('postId').value;

            if (!postId) {
                document.getElementById('commentsResult').innerHTML =
                    '<div class="error">❌ Please enter post ID</div>';
                return;
            }

            fetch('/api/instagram/post-comments?post_id=' + postId)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('commentsResult');
                if (data.success && data.data.data) {
                    const comments = data.data.data;
                    if (comments.length === 0) {
                        resultDiv.innerHTML = '<div class="info">No comments found on this post.</div>';
                    } else {
                        let html = '<div class="success"><strong>Recent Comments:</strong><br>';
                        comments.slice(0, 5).forEach(comment => {
                            html += `<strong>@${comment.username}:</strong> ${comment.text}<br>`;
                            html += `<small>ID: ${comment.id} | ${comment.timestamp}</small><br><br>`;
                        });
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get comments: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('commentsResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function createPost() {
            const imageUrl = document.getElementById('imageUrl').value;
            const caption = document.getElementById('caption').value;
            
            if (!imageUrl) {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Please enter an image URL</div>';
                return;
            }
            
            fetch('/api/instagram/create-post', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image_url: imageUrl,
                    caption: caption
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Instagram post created successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to create post: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function createStory() {
            const imageUrl = document.getElementById('imageUrl').value;
            
            if (!imageUrl) {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Please enter an image URL</div>';
                return;
            }
            
            fetch('/api/instagram/create-story', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    media_url: imageUrl
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Instagram story created successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to create story: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getConversations() {
            fetch('/api/instagram/conversations')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('conversationsResult');
                if (data.success && data.data.data) {
                    const conversations = data.data.data;
                    if (conversations.length === 0) {
                        resultDiv.innerHTML = '<div class="info">No conversations found.</div>';
                    } else {
                        let html = '<div class="success"><strong>Recent Conversations:</strong><br>';
                        conversations.slice(0, 5).forEach(conv => {
                            html += `Conversation ID: ${conv.id}, Participants: ${conv.participants?.data?.length || 0}<br>`;
                        });
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get conversations: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('conversationsResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
