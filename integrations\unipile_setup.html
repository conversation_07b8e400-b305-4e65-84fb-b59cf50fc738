<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unipile API Setup - Unified Social Media Messaging</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #667eea;
            background-color: #f9f9f9;
            border-radius: 0 10px 10px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus, select:focus {
            border-color: #667eea;
            outline: none;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .platform-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .platform-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #ddd;
        }
        .platform-card.connected {
            background-color: #d4edda;
            border-color: #28a745;
        }
        .platform-card.disconnected {
            background-color: #f8d7da;
            border-color: #dc3545;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #667eea;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Unipile API Setup</h1>
        <p style="text-align: center; font-size: 1.2em; color: #666;">
            Unified Social Media Messaging Platform
        </p>
        
        <div class="info">
            <strong>🎯 What is Unipile?</strong><br>
            Unipile provides a unified API to connect and message across multiple social media platforms 
            including WhatsApp, Telegram, Facebook, Instagram, and LinkedIn through a single interface.
        </div>

        <div class="step">
            <h3>Step 1: API Configuration</h3>
            <p>Your Unipile API is pre-configured with the following settings:</p>
            
            <form id="unipileConfigForm">
                <div class="form-group">
                    <label for="apiKey">API Key:</label>
                    <input type="password" id="apiKey" name="apiKey" 
                           value="RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI=" readonly>
                </div>
                
                <div class="form-group">
                    <label for="baseUrl">Base URL:</label>
                    <input type="text" id="baseUrl" name="baseUrl" 
                           value="https://api1.unipile.com:13115/api/v1" readonly>
                </div>
                
                <button type="button" onclick="testConnection()">Test API Connection</button>
                <button type="button" onclick="getAccounts()">Get Connected Accounts</button>
            </form>
            
            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 2: Platform Connection Status</h3>
            <p>Check which social media platforms are connected to your Unipile account:</p>
            
            <div class="platform-status" id="platformStatus">
                <div class="platform-card disconnected">
                    <h4>📱 WhatsApp</h4>
                    <p>Status: Checking...</p>
                </div>
                <div class="platform-card disconnected">
                    <h4>💬 Telegram</h4>
                    <p>Status: Checking...</p>
                </div>
                <div class="platform-card disconnected">
                    <h4>📘 Facebook</h4>
                    <p>Status: Checking...</p>
                </div>
                <div class="platform-card disconnected">
                    <h4>📷 Instagram</h4>
                    <p>Status: Checking...</p>
                </div>
                <div class="platform-card disconnected">
                    <h4>💼 LinkedIn</h4>
                    <p>Status: Checking...</p>
                </div>
            </div>
            
            <button type="button" onclick="checkPlatformStatus()">Refresh Status</button>
            <div id="statusResult"></div>
        </div>

        <div class="step">
            <h3>Step 3: Connect Your Platforms</h3>
            <p>To connect your social media accounts to Unipile:</p>
            <ol>
                <li>Visit the <a href="https://unipile.com" target="_blank">Unipile Dashboard</a></li>
                <li>Log in to your Unipile account</li>
                <li>Go to "Connected Accounts" or "Integrations"</li>
                <li>Follow the platform-specific connection process for each service</li>
                <li>Authorize the required permissions for messaging</li>
            </ol>
            
            <div class="warning">
                <strong>⚠️ Important:</strong> Each platform has specific requirements:
                <ul>
                    <li><strong>WhatsApp:</strong> Requires WhatsApp Business API access</li>
                    <li><strong>Telegram:</strong> Requires bot token from @BotFather</li>
                    <li><strong>Facebook:</strong> Requires Facebook Page and app approval</li>
                    <li><strong>Instagram:</strong> Requires Business account connected to Facebook Page</li>
                    <li><strong>LinkedIn:</strong> Requires LinkedIn app and messaging permissions</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <h3>Step 4: Test Messaging</h3>
            <p>Once your platforms are connected, test the messaging functionality:</p>
            
            <div class="form-group">
                <label for="testPlatform">Platform:</label>
                <select id="testPlatform">
                    <option value="whatsapp">WhatsApp</option>
                    <option value="telegram">Telegram</option>
                    <option value="facebook">Facebook</option>
                    <option value="instagram">Instagram</option>
                    <option value="linkedin">LinkedIn</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="testRecipient">Recipient ID:</label>
                <input type="text" id="testRecipient" placeholder="Phone number, username, or user ID">
            </div>
            
            <div class="form-group">
                <label for="testMessage">Test Message:</label>
                <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from Unipile."></textarea>
            </div>
            
            <button type="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="testResult"></div>
        </div>

        <div class="step">
            <h3>Step 5: Unified Messaging Features</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🚀 Bulk Messaging</h4>
                    <p>Send messages to multiple recipients across different platforms simultaneously.</p>
                </div>
                <div class="feature-card">
                    <h4>📝 Template Messages</h4>
                    <p>Use predefined templates with personalization for consistent messaging.</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Analytics</h4>
                    <p>Track message delivery, read receipts, and engagement across platforms.</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ Rate Limiting</h4>
                    <p>Automatic rate limiting to comply with platform restrictions.</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 Fallback Support</h4>
                    <p>Automatic fallback to individual platform APIs if Unipile is unavailable.</p>
                </div>
                <div class="feature-card">
                    <h4>🛡️ Error Handling</h4>
                    <p>Comprehensive error handling and retry mechanisms.</p>
                </div>
            </div>
        </div>

        <div class="step">
            <h3>Step 6: Usage Examples</h3>
            <p>Here are some code examples for using the unified messaging system:</p>
            
            <div class="code">
# Initialize Unipile messaging
from unified_messaging import UnifiedMessaging, MessageRecipient

unified = UnifiedMessaging(use_unipile=True)

# Send to multiple platforms
recipients = [
    MessageRecipient(platform="whatsapp", recipient_id="+1234567890"),
    MessageRecipient(platform="telegram", recipient_id="@username"),
    MessageRecipient(platform="linkedin", recipient_id="person_id")
]

results = unified.send_bulk_messages(recipients, "Hello from Unipile!")
            </div>
        </div>

        <div class="step">
            <h3>Step 7: Best Practices</h3>
            <ul>
                <li><strong>Respect Rate Limits:</strong> Don't exceed platform-specific rate limits</li>
                <li><strong>Personalize Messages:</strong> Use recipient names and relevant content</li>
                <li><strong>Monitor Delivery:</strong> Check message delivery status and handle failures</li>
                <li><strong>Comply with Policies:</strong> Follow each platform's messaging policies</li>
                <li><strong>Get Consent:</strong> Ensure recipients have opted in to receive messages</li>
                <li><strong>Test Thoroughly:</strong> Test with small groups before bulk messaging</li>
            </ul>
        </div>
    </div>

    <script>
        function testConnection() {
            fetch('/api/unipile/test', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connection successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection failed: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getAccounts() {
            fetch('/api/unipile/accounts', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success && data.accounts) {
                    let html = '<div class="success"><strong>Connected Accounts:</strong><br>';
                    data.accounts.forEach(account => {
                        html += `${account.provider}: ${account.username || account.name || account.account_id}<br>`;
                    });
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get accounts: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function checkPlatformStatus() {
            fetch('/api/unipile/platform-status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updatePlatformCards(data.status);
                    document.getElementById('statusResult').innerHTML = 
                        '<div class="success">✅ Platform status updated!</div>';
                } else {
                    document.getElementById('statusResult').innerHTML = 
                        '<div class="error">❌ Failed to check status: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('statusResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function updatePlatformCards(status) {
            const platforms = ['whatsapp', 'telegram', 'facebook', 'instagram', 'linkedin'];
            const cards = document.querySelectorAll('.platform-card');
            
            cards.forEach((card, index) => {
                const platform = platforms[index];
                const isConnected = status[platform] || false;
                
                card.className = `platform-card ${isConnected ? 'connected' : 'disconnected'}`;
                const statusText = card.querySelector('p');
                statusText.textContent = `Status: ${isConnected ? 'Connected ✅' : 'Not Connected ❌'}`;
            });
        }
        
        function sendTestMessage() {
            const platform = document.getElementById('testPlatform').value;
            const recipient = document.getElementById('testRecipient').value;
            const message = document.getElementById('testMessage').value;
            
            if (!recipient || !message) {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Please enter both recipient and message</div>';
                return;
            }
            
            fetch('/api/unipile/send-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform: platform,
                    recipient_id: recipient,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Test message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send message: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        // Auto-check platform status on page load
        window.addEventListener('load', function() {
            setTimeout(checkPlatformStatus, 1000);
        });
    </script>
</body>
</html>
