#!/usr/bin/env python3
"""
Unified Messaging API Server Startup Script
Launches either Flask or FastAPI server with configuration options
"""

import argparse
import sys
import os
import logging

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging(level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('api_server.log')
        ]
    )

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = {
        'fastapi': 'FastAPI',
        'uvicorn': 'uvicorn',
        'requests': 'requests',
        'pydantic': 'pydantic'
    }
    
    missing_packages = []
    
    for package, display_name in required_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(display_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install fastapi uvicorn requests pydantic")
        return False
    
    return True

def start_fastapi_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """Start FastAPI server"""
    print("🚀 Starting FastAPI server...")
    print(f"📍 Server will be available at: http://{host}:{port}")
    print(f"📖 API documentation: http://{host}:{port}/docs")
    print(f"📖 Alternative docs: http://{host}:{port}/redoc")
    
    try:
        import uvicorn
        uvicorn.run(
            "api_endpoints:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except ImportError:
        print("❌ uvicorn not installed. Install with: pip install uvicorn")
        return False
    except Exception as e:
        print(f"❌ Failed to start FastAPI server: {e}")
        return False

def check_configuration():
    """Check if the system is properly configured"""
    print("🔍 Checking system configuration...")
    
    # Check if unified messaging can be initialized
    try:
        from unified_messaging import UnifiedMessaging
        um = UnifiedMessaging()
        print("✅ Unified messaging system initialized")
        
        # Check platform status
        status = um.get_platform_status()
        configured_platforms = [p for p, s in status.items() if s.get('configured', False)]
        
        if configured_platforms:
            print(f"✅ Configured platforms: {', '.join(configured_platforms)}")
        else:
            print("⚠️  No platforms configured yet")
            
    except Exception as e:
        print(f"⚠️  Unified messaging initialization warning: {e}")
    
    # Check Unipile configuration
    try:
        from unipile_config import is_unipile_available
        if is_unipile_available():
            print("✅ Unipile API configured")
        else:
            print("⚠️  Unipile API not configured")
            print("💡 Set UNIPILE_API_KEY environment variable or update unipile_config.json")
    except Exception as e:
        print(f"⚠️  Unipile check warning: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Unified Messaging API Server")
    parser.add_argument(
        "--framework",
        choices=["fastapi"],
        default="fastapi",
        help="Web framework to use (FastAPI only)"
    )
    parser.add_argument(
        "--host", 
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind to (default: 8000)"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    parser.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    parser.add_argument(
        "--check-only", 
        action="store_true",
        help="Only check configuration, don't start server"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    print("🌟 Unified Messaging API Server")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check configuration
    check_configuration()
    
    if args.check_only:
        print("\n✅ Configuration check completed")
        return
    
    print(f"\n🚀 Starting FastAPI server...")
    print(f"📍 Host: {args.host}")
    print(f"📍 Port: {args.port}")

    # Start FastAPI server
    try:
        start_fastapi_server(args.host, args.port, args.reload)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
