<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Authentication - Unified Messaging</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .auth-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #25D366;
        }

        .auth-section h2 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .qr-code-container {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 10px;
            border: 2px dashed #e1e5e9;
            margin: 20px 0;
        }

        .qr-code {
            max-width: 256px;
            margin: 0 auto;
            border-radius: 10px;
        }

        .qr-placeholder {
            width: 256px;
            height: 256px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border-radius: 10px;
            color: #6c757d;
            font-size: 18px;
            flex-direction: column;
        }

        .qr-instructions {
            margin-top: 20px;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #25D366;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-danger {
            background: #dc3545;
        }

        .result-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e1e5e9;
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #25D366;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-dot.connected {
            background: #25D366;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.3);
        }

        .status-dot.disconnected {
            background: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
        }

        .status-dot.loading {
            background: #ffc107;
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .whatsapp-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .auth-section {
                padding: 20px;
            }

            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="whatsapp-icon">📱</span>WhatsApp Authentication</h1>
            <p>Connect your WhatsApp account to the Unified Messaging System</p>
        </div>

        <div class="content">
            <!-- Connection Status -->
            <div class="auth-section">
                <h2>🔗 Connection Status</h2>
                <div id="connectionStatus" class="status-indicator status-loading">
                    <span class="loading-spinner"></span> Checking connection status...
                </div>
                <button class="btn" onclick="checkConnectionStatus()">Refresh Status</button>
            </div>

            <!-- Unipile QR Code Authentication -->
            <div class="auth-section">
                <h2>🚀 Unipile API Authentication</h2>
                <div class="info">
                    <strong>Unipile provides unified access to WhatsApp with QR code authentication and enhanced features.</strong>
                </div>
                
                <div class="form-group">
                    <label for="unipileApiKey">Unipile API Key:</label>
                    <input type="password" id="unipileApiKey" placeholder="Your Unipile API key">
                </div>
                
                <button class="btn" onclick="connectUnipile()">Connect via Unipile</button>
                <button class="btn btn-secondary" onclick="generateQRCode()">Generate QR Code</button>
                
                <div id="unipileResult"></div>
                
                <!-- QR Code Display Area -->
                <div class="qr-code-container" id="qrCodeContainer" style="display: none;">
                    <div id="qrCodeArea" class="qr-placeholder">
                        <div>📱</div>
                        <div>QR Code will appear here</div>
                    </div>
                    <div class="qr-instructions">
                        <strong>📋 Instructions:</strong>
                        <ol style="text-align: left; margin-top: 10px;">
                            <li>Open WhatsApp on your phone</li>
                            <li>Go to Settings → Linked Devices</li>
                            <li>Tap "Link a Device"</li>
                            <li>Scan this QR code</li>
                        </ol>
                    </div>
                    <button class="btn btn-secondary" onclick="refreshQRCode()">Refresh QR Code</button>
                </div>
            </div>



            <!-- Testing Section -->
            <div class="grid">
                <div class="card">
                    <h3>🧪 Test Messaging</h3>
                    <div class="form-group">
                        <label for="testPhoneNumber">Test Phone Number:</label>
                        <input type="tel" id="testPhoneNumber" placeholder="+**********">
                    </div>
                    <div class="form-group">
                        <label for="testMessage">Test Message:</label>
                        <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from WhatsApp integration."></textarea>
                    </div>
                    <button class="btn" onclick="sendTestMessage()">Send Test Message</button>
                    <div id="testResult"></div>
                </div>

                <div class="card">
                    <h3>📊 Account Information</h3>
                    <button class="btn" onclick="getAccountInfo()">Get Account Info</button>
                    <div id="accountInfoResult"></div>
                </div>

                <div class="card">
                    <h3>📈 Bulk Messaging</h3>
                    <div class="form-group">
                        <label for="bulkRecipients">Recipients (JSON format):</label>
                        <textarea id="bulkRecipients" rows="4" placeholder='[{"phone_number": "+**********", "name": "John"}, {"phone_number": "+**********", "name": "Jane"}]'></textarea>
                    </div>
                    <div class="form-group">
                        <label for="bulkMessage">Message Template:</label>
                        <textarea id="bulkMessage" rows="2" placeholder="Hello {name}! This is a bulk message."></textarea>
                    </div>
                    <button class="btn" onclick="sendBulkMessages()">Send Bulk Messages</button>
                    <div id="bulkResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkConnectionStatus, 30000);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkConnectionStatus();
        });

        function checkConnectionStatus() {
            showLoading('connectionStatus', 'Checking connection status...');

            fetch('/api/whatsapp/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const status = data.status;
                    let html = '<div class="connection-status">';

                    // Unipile status
                    if (status.available) {
                        if (status.connected) {
                            html += '<span class="status-dot connected"></span> Unipile WhatsApp: Connected (' + status.accounts.length + ' accounts)<br>';
                            status.accounts.forEach(account => {
                                html += '&nbsp;&nbsp;• ' + (account.username || account.name || account.account_id) + '<br>';
                            });
                        } else {
                            html += '<span class="status-dot disconnected"></span> Unipile WhatsApp: Available but not connected<br>';
                        }
                    } else {
                        html += '<span class="status-dot disconnected"></span> Unipile WhatsApp: Not available<br>';
                    }

                    html += '</div>';

                    updateConnectionStatus(status.connected ? 'connected' : 'disconnected', html);
                } else {
                    updateConnectionStatus('disconnected', '❌ Failed to get status: ' + data.error);
                }
            })
            .catch(error => {
                updateConnectionStatus('disconnected', '❌ Status check error: ' + error.message);
            });
        }

        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                showResult('unipileResult', '❌ Please enter your Unipile API key', 'error');
                return;
            }
            
            showLoading('unipileResult', 'Connecting to Unipile...');
            
            fetch('/api/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('unipileResult', '✅ Unipile API connected successfully!', 'success');
                    checkConnectionStatus(); // Refresh status
                } else {
                    showResult('unipileResult', '❌ Failed to connect: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('unipileResult', '❌ Connection error: ' + error.message, 'error');
            });
        }

        function generateQRCode() {
            showLoading('unipileResult', 'Generating QR code...');
            
            fetch('/api/whatsapp/authenticate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.qr_code) {
                    document.getElementById('qrCodeArea').innerHTML = 
                        '<img src="' + data.qr_code + '" alt="WhatsApp QR Code" class="qr-code">';
                    document.getElementById('qrCodeContainer').style.display = 'block';
                    showResult('unipileResult', '✅ QR code generated! Please scan with your WhatsApp mobile app.', 'success');
                    
                    // Auto-refresh QR code every 60 seconds
                    setTimeout(refreshQRCode, 60000);
                } else if (data.success && !data.qr_required) {
                    showResult('unipileResult', '✅ WhatsApp account already connected!', 'success');
                    document.getElementById('qrCodeContainer').style.display = 'none';
                } else {
                    showResult('unipileResult', '❌ Failed to generate QR code: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('unipileResult', '❌ QR code generation error: ' + error.message, 'error');
            });
        }

        function refreshQRCode() {
            generateQRCode();
        }



        function sendTestMessage() {
            const phoneNumber = document.getElementById('testPhoneNumber').value;
            const message = document.getElementById('testMessage').value;
            
            if (!phoneNumber || !message) {
                showResult('testResult', '❌ Please enter both phone number and message', 'error');
                return;
            }
            
            showLoading('testResult', 'Sending test message...');
            
            fetch('/api/messaging/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform: 'whatsapp',
                    recipient: phoneNumber,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const method = data.method || 'unknown';
                    showResult('testResult', `✅ Test message sent successfully via ${method}!`, 'success');
                } else {
                    showResult('testResult', '❌ Failed to send test message: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('testResult', '❌ Error: ' + error.message, 'error');
            });
        }

        function getAccountInfo() {
            showLoading('accountInfoResult', 'Getting account information...');

            fetch('/api/whatsapp/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<div class="info"><strong>📊 Account Information:</strong><br>';

                    if (data.status.connected) {
                        html += '<strong>Unipile WhatsApp Accounts:</strong><br>';
                        data.status.accounts.forEach(account => {
                            html += '• ' + (account.username || account.name || account.account_id) + '<br>';
                            if (account.phone_number) {
                                html += '&nbsp;&nbsp;Phone: ' + account.phone_number + '<br>';
                            }
                        });
                    } else {
                        html += 'No WhatsApp accounts connected via Unipile<br>';
                    }

                    html += '<strong>Last Check:</strong> ' + data.status.last_check + '<br>';
                    html += '</div>';
                    document.getElementById('accountInfoResult').innerHTML = html;
                } else {
                    showResult('accountInfoResult', '❌ Failed to get account info: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('accountInfoResult', '❌ Error: ' + error.message, 'error');
            });
        }

        function sendBulkMessages() {
            const recipientsText = document.getElementById('bulkRecipients').value;
            const messageTemplate = document.getElementById('bulkMessage').value;
            
            if (!recipientsText || !messageTemplate) {
                showResult('bulkResult', '❌ Please fill in both recipients and message template', 'error');
                return;
            }
            
            let recipients;
            try {
                recipients = JSON.parse(recipientsText);
            } catch (e) {
                showResult('bulkResult', '❌ Invalid JSON format for recipients', 'error');
                return;
            }
            
            showLoading('bulkResult', `Sending bulk messages to ${recipients.length} recipients...`);
            
            // Convert to campaign format
            const campaign = {
                whatsapp: recipients.map(r => ({
                    contact: r.phone_number,
                    message: messageTemplate.replace('{name}', r.name || 'there')
                }))
            };
            
            fetch('/api/messaging/bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    campaign: campaign,
                    delay: 2.0
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const summary = data.campaign_summary;
                    let html = `<div class="success">✅ Bulk messaging completed!<br>`;
                    html += `Successful: ${summary.successful}, Failed: ${summary.failed}<br>`;
                    html += `Success Rate: ${summary.success_rate.toFixed(1)}%</div>`;
                    document.getElementById('bulkResult').innerHTML = html;
                } else {
                    showResult('bulkResult', '❌ Bulk messaging failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('bulkResult', '❌ Error: ' + error.message, 'error');
            });
        }

        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showLoading(elementId, message = 'Loading...') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="info"><span class="loading-spinner"></span> ${message}</div>`;
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status-indicator status-${status}`;
            statusElement.innerHTML = message;
        }
    </script>
</body>
</html>
