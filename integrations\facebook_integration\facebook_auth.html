<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Messenger Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1877f2;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #1877f2;
            background-color: #f9f9f9;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #1877f2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #166fe5;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #1877f2;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📘 Facebook Messenger Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> Facebook Messenger API requires a Facebook Page and approved app. 
            Make sure you have admin access to a Facebook Page and have completed business verification if required.
        </div>

        <div class="step">
            <h3>Step 1: Create Facebook App</h3>
            <p>1. Go to <a href="https://developers.facebook.com/" target="_blank">Facebook Developers</a></p>
            <p>2. Click "Create App" and select "Business" type</p>
            <p>3. Add "Messenger" product to your app</p>
            <p>4. Complete app review process if needed for production use</p>
        </div>

        <div class="step">
            <h3>Step 2: Connect Your Facebook Page</h3>
            <p>1. In your app dashboard, go to Messenger > Settings</p>
            <p>2. In the "Access Tokens" section, select your Facebook Page</p>
            <p>3. Copy the Page Access Token</p>
            <p>4. Note your Page ID (found in your Facebook Page settings)</p>
        </div>

        <div class="step">
            <h3>Step 3: Configure Your App</h3>
            
            <form id="facebookConfigForm">
                <div class="form-group">
                    <label for="pageAccessToken">Page Access Token:</label>
                    <input type="password" id="pageAccessToken" name="pageAccessToken" 
                           placeholder="Your Facebook Page Access Token">
                </div>
                
                <div class="form-group">
                    <label for="appSecret">App Secret:</label>
                    <input type="password" id="appSecret" name="appSecret" 
                           placeholder="Your Facebook App Secret">
                </div>
                
                <div class="form-group">
                    <label for="pageId">Page ID:</label>
                    <input type="text" id="pageId" name="pageId" 
                           placeholder="Your Facebook Page ID">
                </div>
                
                <div class="form-group">
                    <label for="verifyToken">Webhook Verify Token:</label>
                    <input type="text" id="verifyToken" name="verifyToken" 
                           placeholder="Create a secure verify token for webhooks">
                </div>
                
                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>
            
            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 4: Webhook Setup</h3>
            <p>Configure your webhook in the Facebook App settings:</p>
            <div class="code">
                Webhook URL: https://yourdomain.com/webhook/facebook<br>
                Verify Token: [Use the token you entered above]
            </div>
            <p>Subscribe to these webhook events:</p>
            <ul>
                <li>messages</li>
                <li>messaging_postbacks</li>
                <li>messaging_optins</li>
                <li>message_deliveries</li>
                <li>message_reads</li>
            </ul>
            <button type="button" onclick="setupWebhook()">Setup Webhook</button>
            <div id="webhookResult"></div>
        </div>

        <div class="step">
            <h3>Step 5: Page Setup</h3>
            <p>Configure your Facebook Page for messaging:</p>
            <div class="form-group">
                <label for="greetingText">Greeting Text:</label>
                <textarea id="greetingText" rows="2" placeholder="Welcome! How can we help you today?"></textarea>
            </div>
            <button type="button" onclick="setGreeting()">Set Greeting</button>
            <button type="button" onclick="setGetStarted()">Set Get Started Button</button>
            <div id="pageSetupResult"></div>
        </div>

        <div class="step">
            <h3>Step 6: Test Your Setup</h3>
            <div class="info">
                <strong>Note:</strong> To test messaging, you need a user's Page-Scoped ID (PSID). 
                Users get a PSID when they first interact with your page.
            </div>
            
            <div class="form-group">
                <label for="testUserId">User PSID (Page-Scoped ID):</label>
                <input type="text" id="testUserId" placeholder="User's Page-Scoped ID">
            </div>
            <div class="form-group">
                <label for="testMessage">Test Message:</label>
                <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from Facebook Messenger."></textarea>
            </div>
            <button type="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="testResult"></div>
        </div>

        <div class="step">
            <h3>Step 7: Getting User PSIDs</h3>
            <p>To send messages to users, you need their Page-Scoped IDs (PSIDs):</p>
            <ul>
                <li><strong>Organic:</strong> Users message your page first, webhook receives their PSID</li>
                <li><strong>Opt-in:</strong> Use Messenger plugins on your website</li>
                <li><strong>Customer Chat:</strong> Add customer chat plugin to your website</li>
                <li><strong>Send-to-Messenger:</strong> Use Send-to-Messenger button</li>
            </ul>
            <p><strong>Important:</strong> You can only message users who have initiated contact or opted in.</p>
        </div>

        <div class="step">
            <h3>Step 8: Message Templates</h3>
            <p>For promotional messages outside the 24-hour window, you need approved message templates:</p>
            <ul>
                <li>Go to your Facebook App > Messenger > Message Templates</li>
                <li>Create and submit templates for approval</li>
                <li>Use approved templates for promotional messaging</li>
            </ul>
        </div>
    </div>

    <script>
        function saveConfig() {
            const config = {
                page_access_token: document.getElementById('pageAccessToken').value,
                app_secret: document.getElementById('appSecret').value,
                page_id: document.getElementById('pageId').value,
                verify_token: document.getElementById('verifyToken').value
            };
            
            fetch('/api/facebook/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/facebook/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function setupWebhook() {
            fetch('/api/facebook/webhook-setup')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('webhookResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Webhook setup instructions provided. Please configure in Facebook App settings.</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('webhookResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function setGreeting() {
            const greetingText = document.getElementById('greetingText').value;
            if (!greetingText) {
                document.getElementById('pageSetupResult').innerHTML = 
                    '<div class="error">❌ Please enter greeting text</div>';
                return;
            }
            
            fetch('/api/facebook/greeting', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ greeting: greetingText })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('pageSetupResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Greeting text set successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to set greeting: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('pageSetupResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function setGetStarted() {
            fetch('/api/facebook/get-started', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('pageSetupResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Get Started button configured!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to set Get Started button: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('pageSetupResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendTestMessage() {
            const userId = document.getElementById('testUserId').value;
            const message = document.getElementById('testMessage').value;
            
            if (!userId || !message) {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Please enter both user PSID and message</div>';
                return;
            }
            
            fetch('/api/facebook/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: userId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Test message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send test message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
