# Social Media Messaging Integrations

A comprehensive messaging system that integrates with 6 major social media platforms to send messages to scraped leads. Now featuring **Unipile API** for unified messaging across multiple platforms!

## 🚀 Integration Methods

### 🎯 **Primary: Unipile API (Recommended)**
- **Unified API** for WhatsApp, Telegram, Facebook, Instagram, and LinkedIn
- **Single authentication** for multiple platforms
- **Simplified setup** and management
- **Automatic rate limiting** and error handling
- **Fallback support** to individual platform APIs

### 🔧 **Fallback: Individual Platform APIs**
- **WhatsApp Business API** - Direct messaging via WhatsApp Business
- **Telegram Bot API** - Messaging through Telegram bots
- **Facebook Messenger** - Page messaging via Facebook API
- **Instagram Direct** - Direct messaging for business accounts
- **TikTok for Business** - Engagement through comments (limited messaging)
- **LinkedIn Messaging** - Professional messaging via LinkedIn API

## 📁 Project Structure

```
integrations/
├── 🎯 UNIPILE INTEGRATION (Primary)
│   ├── unipile_api.py          # Unified API client
│   ├── unipile_setup.html      # Setup guide
│   ├── unipile_config.json     # Configuration
│   └── test_unipile.py         # Test script
├── whatsapp_integration/
│   ├── whatsapp_api.py
│   ├── whatsapp_auth.html
│   └── config.json
├── telegram_integration/
│   ├── telegram_api.py
│   ├── telegram_auth.html
│   └── config.json
├── facebook_integration/
│   ├── facebook_api.py
│   ├── facebook_auth.html
│   └── config.json
├── instagram_integration/
│   ├── instagram_api.py
│   ├── instagram_auth.html
│   └── config.json
├── tiktok_integration/
│   ├── tiktok_api.py
│   ├── tiktok_auth.html
│   └── config.json
├── linkedin_integration/
│   ├── linkedin_api.py
│   ├── linkedin_auth.html
│   └── config.json
├── unified_messaging.py        # Enhanced with Unipile
└── README.md
```

## 🔧 Installation & Setup

### Prerequisites

```bash
pip install requests asyncio concurrent.futures dataclasses
```

### 🎯 Quick Setup with Unipile (Recommended)

1. **Open Unipile Setup**: `unipile_setup.html`
2. **Test Connection**: Run `python test_unipile.py`
3. **Connect Platforms**: Use Unipile dashboard to connect your social media accounts
4. **Start Messaging**: Use the unified messaging system

### 🔧 Alternative: Individual Platform Setup

Each platform requires specific setup and authentication. Open the respective `*_auth.html` files in your browser for detailed setup instructions:

1. **WhatsApp**: `whatsapp_integration/whatsapp_auth.html`
2. **Telegram**: `telegram_integration/telegram_auth.html`
3. **Facebook**: `facebook_integration/facebook_auth.html`
4. **Instagram**: `instagram_integration/instagram_auth.html`
5. **TikTok**: `tiktok_integration/tiktok_auth.html`
6. **LinkedIn**: `linkedin_integration/linkedin_auth.html`

## 🚀 Quick Start

### 1. 🎯 Unipile API Usage (Recommended)

```python
# Test Unipile connection
from test_unipile import test_unipile_connection, test_platform_status

# Test API connection
test_unipile_connection()

# Check platform status
test_platform_status()

# Direct Unipile usage
from unipile_api import UnipileAPI

unipile = UnipileAPI()
result = unipile.send_whatsapp_message("+1234567890", "Hello from Unipile!")
print(result)
```

### 2. 🔄 Unified Messaging System (With Unipile)

```python
from unified_messaging import UnifiedMessaging, MessageRecipient

# Initialize with Unipile (default)
unified = UnifiedMessaging(use_unipile=True)

# Check platform status
status = unified.get_platform_status()
print("Platform Status:", status)

# Send to multiple platforms
recipients = [
    MessageRecipient(platform="whatsapp", recipient_id="+1234567890"),
    MessageRecipient(platform="telegram", recipient_id="@username"),
    MessageRecipient(platform="facebook", recipient_id="user_psid")
]

results = unified.send_bulk_messages(recipients, "Hello from all platforms!")
print("Results:", results)
```

### 3. 📝 Template Messaging

```python
# Send template messages
results = unified.send_template_message(
    recipients,
    "welcome",
    name="John Doe",
    company="Your Company"
)
```

### 4. 🔧 Fallback to Individual APIs

```python
# Use individual platform APIs as fallback
unified = UnifiedMessaging(use_unipile=False)

# Or individual platform usage
from whatsapp_integration.whatsapp_api import WhatsAppAPI

whatsapp = WhatsAppAPI()
if whatsapp.is_configured():
    result = whatsapp.send_text_message("+1234567890", "Hello from WhatsApp!")
    print(result)
```

## 📊 Features by Platform

### WhatsApp Business API
- ✅ Text messages
- ✅ Template messages
- ✅ Media messages
- ✅ Message status tracking
- ✅ Webhook support

### Telegram Bot API
- ✅ Text messages
- ✅ Photo/document messages
- ✅ Inline keyboards
- ✅ Webhook support
- ✅ Bulk messaging

### Facebook Messenger
- ✅ Text messages
- ✅ Image messages
- ✅ Button templates
- ✅ Quick replies
- ✅ Persistent menu

### Instagram Direct
- ✅ Direct messages
- ✅ Media messages
- ✅ Story creation
- ✅ Post creation
- ✅ Analytics

### TikTok for Business
- ⚠️ Limited messaging (no direct DM API)
- ✅ Content upload
- ✅ Comment replies
- ✅ Analytics
- ✅ Video management

### LinkedIn Messaging
- ✅ Direct messages (1st connections only)
- ✅ Connection requests
- ✅ Post creation
- ✅ Profile management
- ⚠️ Strict rate limits

## 🔒 Authentication Requirements

### WhatsApp
- Facebook Business Account
- WhatsApp Business API access
- Phone Number ID
- Access Token

### Telegram
- Bot Token from @BotFather
- No additional verification needed

### Facebook
- Facebook App
- Page Access Token
- App Secret
- Webhook verification

### Instagram
- Business/Creator account
- Connected Facebook Page
- Instagram Account ID
- Access Token

### TikTok
- TikTok Developer Account (approval required)
- Client Key/Secret
- OAuth access tokens

### LinkedIn
- LinkedIn App
- Client ID/Secret
- OAuth access tokens
- API approval for messaging

## ⚡ Rate Limits

| Platform | Messages/Second | Messages/Minute | Messages/Hour |
|----------|----------------|-----------------|---------------|
| WhatsApp | 1 | 60 | 1000 |
| Telegram | 30 | 20/chat | Unlimited |
| Facebook | 10 | 600 | 10000 |
| Instagram | 5 | 200 | 1000 |
| TikTok | 10 | 100 | 1000 |
| LinkedIn | 10 | 100 | 500 |

## 🛡️ Best Practices

### Message Content
- Keep messages professional and relevant
- Personalize messages when possible
- Follow platform-specific guidelines
- Respect user privacy and consent

### Rate Limiting
- Implement delays between messages
- Monitor API quotas
- Use exponential backoff for retries
- Batch messages when possible

### Error Handling
- Log all API responses
- Implement retry mechanisms
- Handle platform-specific errors
- Monitor delivery status

## 📈 Analytics & Monitoring

```python
# Get platform analytics
analytics = unified.get_platform_analytics()
print("Analytics:", analytics)

# Export message results
results = unified.send_bulk_messages(recipients, "Test message")
filename = unified.export_message_results(results)
print(f"Results exported to: {filename}")
```

## 🚨 Important Limitations

### TikTok
- No direct messaging API available
- Focus on content creation and comment engagement
- Requires developer approval

### LinkedIn
- Can only message 1st-degree connections
- Strict rate limits and approval requirements
- Professional use only

### Instagram
- Requires business account
- 24-hour messaging window
- Limited to users who initiated contact

## 🔧 Configuration

Each platform has its own `config.json` file with the following structure:

```json
{
  "api_credentials": "...",
  "rate_limit": {
    "messages_per_second": 10,
    "messages_per_minute": 100
  },
  "settings": {
    "auto_retry": true,
    "max_retries": 3,
    "log_level": "INFO"
  },
  "message_templates": {
    "welcome": "Welcome message template",
    "follow_up": "Follow-up message template"
  }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## ⚠️ Disclaimer

- Ensure compliance with each platform's Terms of Service
- Respect user privacy and data protection laws
- Obtain proper consent before sending messages
- Monitor for spam and abuse prevention
- Some features require platform approval

## 🆘 Support

For setup help, check the individual `*_auth.html` files for each platform's detailed configuration instructions.
