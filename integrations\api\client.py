"""
Unified Messaging API Client
Python client library for the Unified Messaging API
"""

import requests
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

class UnifiedMessagingClient:
    """Client for the Unified Messaging API"""
    
    def __init__(self, base_url: str = "http://localhost:5000/api", timeout: int = 30):
        """
        Initialize the API client
        
        Args:
            base_url: Base URL of the API server
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """Make HTTP request to the API"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=params, timeout=self.timeout)
            elif method.upper() == "POST":
                response = self.session.post(url, json=data, params=params, timeout=self.timeout)
            elif method.upper() == "PUT":
                response = self.session.put(url, json=data, params=params, timeout=self.timeout)
            elif method.upper() == "DELETE":
                response = self.session.delete(url, params=params, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    return {"success": False, "error": error_data.get("error", str(e))}
                except:
                    return {"success": False, "error": str(e)}
            return {"success": False, "error": str(e)}
    
    def health_check(self) -> Dict:
        """Check API health status"""
        return self._make_request("GET", "health")
    
    def send_message(self, platform: str, recipient: str, message: str, **options) -> Dict:
        """
        Send single message
        
        Args:
            platform: Platform name (whatsapp, telegram, facebook, instagram, linkedin, tiktok)
            recipient: Recipient ID (phone number, username, etc.)
            message: Message content
            **options: Platform-specific options
        
        Returns:
            Dict with success status and message details
        """
        data = {
            "platform": platform,
            "recipient": recipient,
            "message": message,
            "options": options
        }
        
        return self._make_request("POST", "messaging/send", data)
    
    def send_bulk_messages(self, campaign: Dict[str, List[Dict]], delay: float = 2.0) -> Dict:
        """
        Send bulk messages across multiple platforms
        
        Args:
            campaign: Dictionary with platform names as keys and lists of recipient data as values
            delay: Delay between messages in seconds
        
        Returns:
            Dict with campaign results and summary
        """
        data = {
            "campaign": campaign,
            "delay": delay
        }
        
        return self._make_request("POST", "messaging/bulk", data)
    
    def send_template_message(self, template_name: str, recipients: List[Dict], variables: Dict = None) -> Dict:
        """
        Send template message to multiple recipients
        
        Args:
            template_name: Name of the template to use
            recipients: List of recipients with platform and contact info
            variables: Variables to substitute in the template
        
        Returns:
            Dict with template message results
        """
        data = {
            "template_name": template_name,
            "recipients": recipients,
            "variables": variables or {}
        }
        
        return self._make_request("POST", "messaging/template", data)
    
    def get_platform_status(self, platform: str = "all") -> Dict:
        """
        Get platform connection status
        
        Args:
            platform: Platform name or 'all' for all platforms
        
        Returns:
            Dict with platform status information
        """
        return self._make_request("GET", f"messaging/status/{platform}")
    
    def get_analytics(self) -> Dict:
        """Get message analytics and statistics"""
        return self._make_request("GET", "messaging/analytics")
    
    def get_templates(self) -> Dict:
        """Get available message templates"""
        return self._make_request("GET", "config/templates")
    
    def get_supported_platforms(self) -> Dict:
        """Get list of supported platforms"""
        return self._make_request("GET", "config/platforms")
    
    def update_platform_config(self, platform: str, config: Dict) -> Dict:
        """
        Update platform-specific configuration
        
        Args:
            platform: Platform name
            config: Configuration data
        
        Returns:
            Dict with update result
        """
        return self._make_request("POST", f"{platform}/config", config)
    
    def test_platform_connection(self, platform: str) -> Dict:
        """
        Test platform connection
        
        Args:
            platform: Platform name
        
        Returns:
            Dict with test result
        """
        return self._make_request("GET", f"{platform}/test")
    
    def connect_unipile(self, api_key: str) -> Dict:
        """
        Connect to Unipile API
        
        Args:
            api_key: Unipile API key
        
        Returns:
            Dict with connection result
        """
        data = {"api_key": api_key}
        return self._make_request("POST", "unipile/connect", data)
    
    def get_unipile_accounts(self) -> Dict:
        """Get Unipile connected accounts"""
        return self._make_request("GET", "unipile/accounts")
    
    # Convenience methods for specific platforms
    def send_whatsapp_message(self, phone_number: str, message: str, **options) -> Dict:
        """Send WhatsApp message"""
        return self.send_message("whatsapp", phone_number, message, **options)
    
    def send_telegram_message(self, chat_id: str, message: str, **options) -> Dict:
        """Send Telegram message"""
        return self.send_message("telegram", chat_id, message, **options)
    
    def send_facebook_message(self, recipient_id: str, message: str, **options) -> Dict:
        """Send Facebook Messenger message"""
        return self.send_message("facebook", recipient_id, message, **options)
    
    def send_instagram_message(self, recipient_id: str, message: str, **options) -> Dict:
        """Send Instagram direct message"""
        return self.send_message("instagram", recipient_id, message, **options)
    
    def send_linkedin_message(self, recipient_id: str, message: str, **options) -> Dict:
        """Send LinkedIn message"""
        return self.send_message("linkedin", recipient_id, message, **options)
    
    def send_tiktok_message(self, recipient_id: str, message: str, **options) -> Dict:
        """Send TikTok message"""
        return self.send_message("tiktok", recipient_id, message, **options)
    
    # Batch operations
    def send_whatsapp_bulk(self, recipients: List[Dict], delay: float = 2.0) -> Dict:
        """Send bulk WhatsApp messages"""
        campaign = {"whatsapp": recipients}
        return self.send_bulk_messages(campaign, delay)
    
    def send_telegram_bulk(self, recipients: List[Dict], delay: float = 1.0) -> Dict:
        """Send bulk Telegram messages"""
        campaign = {"telegram": recipients}
        return self.send_bulk_messages(campaign, delay)
    
    def send_cross_platform_campaign(self, platforms: List[str], recipients: List[Dict], 
                                   message_template: str, delay: float = 2.0) -> Dict:
        """
        Send cross-platform campaign
        
        Args:
            platforms: List of platform names
            recipients: List of recipients with contact info
            message_template: Message template with placeholders
            delay: Delay between messages
        
        Returns:
            Dict with campaign results
        """
        campaign = {}
        
        for platform in platforms:
            campaign[platform] = []
            for recipient in recipients:
                # Format message with recipient data
                try:
                    message = message_template.format(**recipient)
                except KeyError:
                    message = message_template
                
                campaign[platform].append({
                    "contact": recipient.get("contact", ""),
                    "message": message
                })
        
        return self.send_bulk_messages(campaign, delay)

# Example usage and testing
if __name__ == "__main__":
    # Initialize client
    client = UnifiedMessagingClient()
    
    # Test health check
    health = client.health_check()
    print("Health check:", health)
    
    # Test platform status
    status = client.get_platform_status()
    print("Platform status:", status)
    
    # Example: Send single message (uncomment to test)
    # result = client.send_whatsapp_message("+**********", "Hello from API client!")
    # print("WhatsApp message result:", result)
    
    # Example: Send bulk campaign (uncomment to test)
    # campaign = {
    #     "whatsapp": [
    #         {"contact": "+**********", "message": "Hello WhatsApp!"},
    #         {"contact": "+**********", "message": "Hi there!"}
    #     ],
    #     "telegram": [
    #         {"contact": "@username", "message": "Hello Telegram!"}
    #     ]
    # }
    # bulk_result = client.send_bulk_messages(campaign)
    # print("Bulk campaign result:", bulk_result)
    
    # Example: Send template message (uncomment to test)
    # template_result = client.send_template_message(
    #     "welcome",
    #     [
    #         {"platform": "whatsapp", "contact": "+**********", "name": "John"},
    #         {"platform": "telegram", "contact": "@username", "name": "Jane"}
    #     ],
    #     {"company": "ACME Corp"}
    # )
    # print("Template message result:", template_result)
    
    # Get analytics
    analytics = client.get_analytics()
    print("Analytics:", analytics)
    
    # Get available templates
    templates = client.get_templates()
    print("Available templates:", templates)
    
    print("\n🎉 API Client test completed!")
    print("💡 Uncomment example calls above to test actual messaging")
